'use client';

import { useEffect, useState, useCallback } from 'react';
import { preloadUIImages, getUICacheStats } from '@/lib/asset-loader';

/**
 * UI Image Preloader Component for 360° Viewer System
 * Preloads navigation icons, interface graphics, and UI elements
 * Provides loading progress and cache statistics
 */
export default function UIImagePreloader({ 
  onPreloadComplete = null,
  showProgress = false,
  priority = 'normal'
}) {
  const [preloadStatus, setPreloadStatus] = useState({
    loading: false,
    progress: 0,
    total: 0,
    loaded: 0,
    errors: [],
    complete: false
  });

  // Define UI images to preload for 360° viewer
  const uiImages = [
    // Navigation icons
    { src: '/assets/guide_btn_off.png', priority: 'high' },
    { src: '/assets/guide_btn_ov.png', priority: 'high' },
    { src: '/assets/upstairs_btn_off.png', priority: 'normal' },
    { src: '/assets/upstairs_btn_ov.png', priority: 'normal' },
    { src: '/assets/downstairs_btn_off.png', priority: 'normal' },
    { src: '/assets/downstairs_btn_ov.png', priority: 'normal' },
    { src: '/assets/entrance_btn_off.png', priority: 'high' },
    { src: '/assets/entrance_btn_ov.png', priority: 'high' },
    
    // Info markers
    { src: '/assets/photo_btn_off.png', priority: 'normal' },
    { src: '/assets/photo_btn_ov.png', priority: 'normal' },
    { src: '/assets/video_btn_off.png', priority: 'normal' },
    { src: '/assets/video_btn_ov.png', priority: 'normal' },
    { src: '/assets/read_more_btn_off.png', priority: 'normal' },
    { src: '/assets/read_more_btn_ov.png', priority: 'normal' },
    
    // Room navigation
    { src: '/assets/bedroom_1_btn_off.png', priority: 'normal' },
    { src: '/assets/bedroom_1_btn_ov.png', priority: 'normal' },
    { src: '/assets/bedroom_2_btn_off.png', priority: 'normal' },
    { src: '/assets/bedroom_2_btn_ov.png', priority: 'normal' },
    { src: '/assets/dining_btn_off.png', priority: 'normal' },
    { src: '/assets/dining_btn_ov.png', priority: 'normal' },
    { src: '/assets/lounge_btn_off.png', priority: 'normal' },
    { src: '/assets/lounge_btn_ov.png', priority: 'normal' },
    { src: '/assets/master_bedroom_btn_off.png', priority: 'normal' },
    { src: '/assets/master_bedroom_btn_ov.png', priority: 'normal' },
    { src: '/assets/terrace_btn_off.png', priority: 'normal' },
    { src: '/assets/terrace_btn_ov.png', priority: 'normal' },
    { src: '/assets/balcony_btn_off.png', priority: 'normal' },
    { src: '/assets/balcony_btn_ov.png', priority: 'normal' },
    
    // View directions
    { src: '/assets/north_view_btn_off.png', priority: 'low' },
    { src: '/assets/north_view_btn_ov.png', priority: 'low' },
    { src: '/assets/south_view_btn_off.png', priority: 'low' },
    { src: '/assets/south_view_btn_ov.png', priority: 'low' },
    { src: '/assets/east_view_btn_off.png', priority: 'low' },
    { src: '/assets/east_view_btn_ov.png', priority: 'low' },
    { src: '/assets/west_view_btn_off.png', priority: 'low' },
    { src: '/assets/west_view_btn_ov.png', priority: 'low' },
    
    // Interface elements
    { src: '/assets/elephant_island_logo.png', priority: 'high' },
    { src: '/assets/elephant_island_logo_white.png', priority: 'high' },
    { src: '/assets/swipe_icon.png', priority: 'normal' },
  ];

  const handlePreloadProgress = useCallback((progressData) => {
    setPreloadStatus(prev => ({
      ...prev,
      loaded: progressData.loaded,
      total: progressData.total,
      progress: Math.round((progressData.loaded / progressData.total) * 100),
      errors: progressData.success ? prev.errors : [...prev.errors, progressData.error]
    }));
  }, []);

  const startPreloading = useCallback(async () => {
    setPreloadStatus(prev => ({
      ...prev,
      loading: true,
      complete: false,
      progress: 0,
      loaded: 0,
      total: uiImages.length,
      errors: []
    }));

    try {
      const results = await preloadUIImages(uiImages, {
        concurrency: 3,
        priority,
        onProgress: handlePreloadProgress
      });

      const successCount = results.filter(r => r.success).length;
      const errorCount = results.filter(r => !r.success).length;

      setPreloadStatus(prev => ({
        ...prev,
        loading: false,
        complete: true,
        progress: 100,
        loaded: successCount,
        errors: results.filter(r => !r.success).map(r => r.error)
      }));

      if (onPreloadComplete) {
        onPreloadComplete({
          success: errorCount === 0,
          loaded: successCount,
          errors: errorCount,
          total: uiImages.length
        });
      }

    } catch (error) {
      console.error('UI image preloading failed:', error);
      setPreloadStatus(prev => ({
        ...prev,
        loading: false,
        complete: true,
        errors: [...prev.errors, error]
      }));
    }
  }, [uiImages, priority, handlePreloadProgress, onPreloadComplete]);

  // Start preloading on mount
  useEffect(() => {
    startPreloading();
  }, [startPreloading]);

  // Don't render anything if progress display is disabled
  if (!showProgress) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-3 text-white text-xs max-w-xs">
      <div className="flex items-center justify-between mb-2">
        <span className="font-medium">UI Assets</span>
        <span className="text-white/70">{preloadStatus.progress}%</span>
      </div>
      
      <div className="w-full bg-white/20 rounded-full h-1.5 mb-2">
        <div 
          className="bg-blue-400 h-1.5 rounded-full transition-all duration-300"
          style={{ width: `${preloadStatus.progress}%` }}
        />
      </div>
      
      <div className="flex justify-between text-white/70">
        <span>{preloadStatus.loaded}/{preloadStatus.total}</span>
        {preloadStatus.errors.length > 0 && (
          <span className="text-red-400">{preloadStatus.errors.length} errors</span>
        )}
      </div>
      
      {preloadStatus.complete && (
        <div className="mt-2 text-green-400 text-center">
          ✓ Preloading complete
        </div>
      )}
    </div>
  );
}

/**
 * Hook for getting UI cache statistics
 */
export function useUICacheStats() {
  const [stats, setStats] = useState(null);

  useEffect(() => {
    const updateStats = () => {
      setStats(getUICacheStats());
    };

    updateStats();
    const interval = setInterval(updateStats, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return stats;
}
