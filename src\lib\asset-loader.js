'use client';

import React from 'react';

/**
 * Enhanced Asset Loading Utility
 * Provides caching, error handling, and fallback mechanisms for asset loading
 * Specifically designed to prevent 429 errors and improve performance
 * Enhanced with comprehensive UI image caching for 360° viewer components
 */

// Asset cache to prevent repeated requests
const assetCache = new Map();
const loadingPromises = new Map();
const uiImageCache = new Map(); // Dedicated cache for UI elements

// Configuration
const ASSET_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
  requestTimeout: 10000, // 10 seconds
  uiCacheTimeout: 30 * 60 * 1000, // 30 minutes for UI elements
};

/**
 * Enhanced image preloader with caching and error handling
 * @param {string} src - Image source URL
 * @param {Object} options - Loading options
 * @returns {Promise<HTMLImageElement>}
 */
export async function loadImageWithCache(src, options = {}) {
  const {
    maxRetries = ASSET_CONFIG.maxRetries,
    retryDelay = ASSET_CONFIG.retryDelay,
    timeout = ASSET_CONFIG.requestTimeout,
  } = options;

  // Check cache first
  const cacheKey = src;
  const cached = assetCache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < ASSET_CONFIG.cacheTimeout) {
    return cached.image;
  }

  // Check if already loading
  if (loadingPromises.has(cacheKey)) {
    return loadingPromises.get(cacheKey);
  }

  // Create loading promise
  const loadingPromise = loadImageWithRetry(src, maxRetries, retryDelay, timeout);
  loadingPromises.set(cacheKey, loadingPromise);

  try {
    const image = await loadingPromise;
    
    // Cache successful result
    assetCache.set(cacheKey, {
      image,
      timestamp: Date.now(),
    });
    
    return image;
  } catch (error) {
    console.warn(`Failed to load image after ${maxRetries} retries:`, src, error);
    throw error;
  } finally {
    loadingPromises.delete(cacheKey);
  }
}

/**
 * Load image with retry logic
 * @param {string} src - Image source URL
 * @param {number} maxRetries - Maximum retry attempts
 * @param {number} retryDelay - Delay between retries
 * @param {number} timeout - Request timeout
 * @returns {Promise<HTMLImageElement>}
 */
async function loadImageWithRetry(src, maxRetries, retryDelay, timeout) {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await loadImageWithTimeout(src, timeout);
    } catch (error) {
      lastError = error;
      
      if (attempt < maxRetries) {
        // Add exponential backoff to prevent overwhelming the server
        const delay = retryDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

/**
 * Load image with timeout
 * @param {string} src - Image source URL
 * @param {number} timeout - Request timeout
 * @returns {Promise<HTMLImageElement>}
 */
function loadImageWithTimeout(src, timeout) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    let timeoutId;

    const cleanup = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      img.onload = null;
      img.onerror = null;
    };

    // Set timeout
    timeoutId = setTimeout(() => {
      cleanup();
      reject(new Error(`Image loading timeout: ${src}`));
    }, timeout);

    img.onload = () => {
      cleanup();
      resolve(img);
    };

    img.onerror = (error) => {
      cleanup();
      reject(new Error(`Image loading failed: ${src} - ${error.message || 'Unknown error'}`));
    };

    // Set crossOrigin for Three.js compatibility
    // Always set crossOrigin for local development to prevent CORS issues
    img.crossOrigin = 'anonymous';

    // Start loading
    img.src = src;
  });
}

/**
 * Preload multiple images with priority-based loading
 * @param {Array} imageSources - Array of image sources with optional priority
 * @param {Object} options - Loading options
 * @returns {Promise<Map>} - Map of src to loaded images
 */
export async function preloadImages(imageSources, options = {}) {
  const {
    concurrency = 3, // Limit concurrent requests to prevent 429 errors
    priorityFirst = true,
  } = options;

  // Sort by priority if specified
  const sortedSources = priorityFirst 
    ? [...imageSources].sort((a, b) => (a.priority || 0) - (b.priority || 0))
    : imageSources;

  const results = new Map();
  const errors = [];

  // Process in batches to control concurrency
  for (let i = 0; i < sortedSources.length; i += concurrency) {
    const batch = sortedSources.slice(i, i + concurrency);
    
    const batchPromises = batch.map(async (source) => {
      const src = typeof source === 'string' ? source : source.src;
      
      try {
        const image = await loadImageWithCache(src, options);
        results.set(src, image);
        return { src, success: true, image };
      } catch (error) {
        errors.push({ src, error });
        return { src, success: false, error };
      }
    });

    // Wait for current batch to complete before starting next
    await Promise.allSettled(batchPromises);
    
    // Add small delay between batches to prevent overwhelming the server
    if (i + concurrency < sortedSources.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  if (errors.length > 0) {
    console.warn(`Failed to load ${errors.length} images:`, errors);
  }

  return results;
}

/**
 * Enhanced UI image loader with dedicated caching for interface elements
 * @param {string} src - Image source URL
 * @param {Object} options - Loading options
 * @returns {Promise<HTMLImageElement>}
 */
export async function loadUIImageWithCache(src, options = {}) {
  const {
    maxRetries = ASSET_CONFIG.maxRetries,
    retryDelay = ASSET_CONFIG.retryDelay,
    timeout = ASSET_CONFIG.requestTimeout,
    priority = 'normal', // 'high', 'normal', 'low'
  } = options;

  // Check UI cache first with longer timeout
  const cacheKey = src;
  const cached = uiImageCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < ASSET_CONFIG.uiCacheTimeout) {
    return cached.image;
  }

  // Check if already loading
  if (loadingPromises.has(cacheKey)) {
    return loadingPromises.get(cacheKey);
  }

  // Create loading promise with priority handling
  const loadingPromise = loadImageWithRetry(src, maxRetries, retryDelay, timeout);
  loadingPromises.set(cacheKey, loadingPromise);

  try {
    const image = await loadingPromise;

    // Cache successful result in UI cache
    uiImageCache.set(cacheKey, {
      image,
      timestamp: Date.now(),
      priority,
    });

    return image;
  } catch (error) {
    console.warn(`Failed to load UI image after ${maxRetries} retries:`, src, error);
    throw error;
  } finally {
    loadingPromises.delete(cacheKey);
  }
}

/**
 * Preload UI images for 360° viewer interface
 * @param {Array} imageSources - Array of UI image sources to preload
 * @param {Object} options - Preloading options
 * @returns {Promise<Array>}
 */
export async function preloadUIImages(imageSources, options = {}) {
  const {
    concurrency = 3,
    priority = 'normal',
    onProgress = null,
  } = options;

  const results = [];
  const errors = [];

  // Sort by priority if specified
  const sortedSources = imageSources.map(src => ({
    src: typeof src === 'string' ? src : src.src,
    priority: typeof src === 'string' ? priority : (src.priority || priority),
  })).sort((a, b) => {
    const priorityOrder = { high: 3, normal: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });

  // Process in batches
  for (let i = 0; i < sortedSources.length; i += concurrency) {
    const batch = sortedSources.slice(i, i + concurrency);
    const batchPromises = batch.map(async ({ src, priority: itemPriority }) => {
      try {
        const image = await loadUIImageWithCache(src, { priority: itemPriority });
        results.push({ src, image, success: true });

        if (onProgress) {
          onProgress({
            loaded: results.length,
            total: sortedSources.length,
            src,
            success: true,
          });
        }

        return image;
      } catch (error) {
        errors.push({ src, error });
        results.push({ src, error, success: false });

        if (onProgress) {
          onProgress({
            loaded: results.length,
            total: sortedSources.length,
            src,
            success: false,
            error,
          });
        }

        return null;
      }
    });

    // Wait for current batch to complete before starting next
    await Promise.allSettled(batchPromises);

    // Add small delay between batches to prevent overwhelming the server
    if (i + concurrency < sortedSources.length) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  if (errors.length > 0) {
    console.warn(`Failed to load ${errors.length} UI images:`, errors);
  }

  return results;
}

/**
 * Clear asset cache
 * @param {string} src - Optional specific source to clear, or clear all if not provided
 * @param {boolean} includeUI - Whether to clear UI cache as well
 */
export function clearAssetCache(src = null, includeUI = false) {
  if (src) {
    assetCache.delete(src);
    loadingPromises.delete(src);
    if (includeUI) {
      uiImageCache.delete(src);
    }
  } else {
    assetCache.clear();
    loadingPromises.clear();
    if (includeUI) {
      uiImageCache.clear();
    }
  }
}

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
export function getCacheStats() {
  return {
    cacheSize: assetCache.size,
    uiCacheSize: uiImageCache.size,
    loadingCount: loadingPromises.size,
    totalCacheSize: assetCache.size + uiImageCache.size,
    cacheEntries: Array.from(assetCache.keys()),
  };
}

/**
 * Get UI-specific cache statistics
 * @returns {Object} UI cache statistics
 */
export function getUICacheStats() {
  const stats = {
    total: uiImageCache.size,
    byPriority: { high: 0, normal: 0, low: 0 },
    oldestEntry: null,
    newestEntry: null,
  };

  let oldestTime = Date.now();
  let newestTime = 0;

  for (const [src, data] of uiImageCache.entries()) {
    const priority = data.priority || 'normal';
    stats.byPriority[priority]++;

    if (data.timestamp < oldestTime) {
      oldestTime = data.timestamp;
      stats.oldestEntry = { src, timestamp: data.timestamp };
    }

    if (data.timestamp > newestTime) {
      newestTime = data.timestamp;
      stats.newestEntry = { src, timestamp: data.timestamp };
    }
  }

  return stats;
}

/**
 * React hook for loading images with caching
 * @param {string} src - Image source URL
 * @param {Object} options - Loading options
 * @returns {Object} Loading state and image
 */
export function useImageLoader(src, options = {}) {
  const [state, setState] = React.useState({
    loading: true,
    error: null,
    image: null,
  });

  React.useEffect(() => {
    if (!src) {
      setState({ loading: false, error: null, image: null });
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    loadImageWithCache(src, options)
      .then(image => {
        setState({ loading: false, error: null, image });
      })
      .catch(error => {
        setState({ loading: false, error, image: null });
      });
  }, [src]);

  return state;
}
