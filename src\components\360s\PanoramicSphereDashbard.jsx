'use client';

import { useRef, useEffect, useState, useMemo, useCallback, memo } from 'react';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';
import { useControls } from 'leva';
import { useThree } from '@react-three/fiber'; // Import useThree to get access to camera

// Memoized OrbitControls component to prevent unnecessary re-renders
const MemoizedOrbitControls = memo(OrbitControls);

function PanoramicSphere({
  currentImage,
  set_360Object, // This prop allows updating the parent state
  imageUrl,
  imageId,
  textureCache,
  setTextureCache,
  loadingQueue,
  setLoadingQueue,
  onTextureLoad,
  resetView,
}) {
  const meshRef = useRef();
  const controlsRef = useRef();
  const debounceTimeoutRef = useRef(null);
  const [currentTexture, setCurrentTexture] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const { camera } = useThree(); // Get the R3F camera instance

  // Memoized geometry and material for better performance
  const sphereGeometry = useMemo(() => new THREE.SphereGeometry(32, 60, 40), []);

  const basicMaterial = useMemo(() => {
    const material = new THREE.MeshBasicMaterial({
      side: THREE.BackSide,
      transparent: false,
    });
    return material;
  }, []);

  // Memoize the texture loader to avoid re-creation
  const textureLoader = useMemo(() => new THREE.TextureLoader(), []);

  // Memoized OrbitControls configuration
  const controlsConfig = useMemo(() => ({
    enableZoom: false,
    enablePan: false,
    enableRotate: true,
    enableDamping: true,
    dampingFactor: 0.05,
    rotateSpeed: -0.35,
    minPolarAngle: 0,
    maxPolarAngle: Math.PI,
    minAzimuthAngle: -Infinity,
    maxAzimuthAngle: Infinity,
  }), []);

  // Cleanup geometries, materials, textures, and timeouts on unmount
  useEffect(() => {
    return () => {
      sphereGeometry?.dispose();
      basicMaterial?.dispose();
      currentTexture?.dispose(); // Dispose of the texture if it exists
      // Cleanup debounce timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [sphereGeometry, basicMaterial, currentTexture]);

  // Optimized texture configuration function
  const configureTexture = useCallback((texture) => {
    texture.mapping = THREE.EquirectangularReflectionMapping;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.flipY = true;
    // Set proper color space for accurate color representation
    texture.colorSpace = THREE.SRGBColorSpace;
    texture.needsUpdate = true;
    return texture;
  }, []);

  // Optimized texture loading with better error handling and performance
  const loadTexture = useCallback(async (url, id) => {
    if (textureCache.has(id)) {
      return textureCache.get(id);
    }

    setIsLoading(true);

    try {
      const texture = await new Promise((resolve, reject) => {
        const loader = textureLoader;

        const timeoutId = setTimeout(() => {
          reject(new Error('Texture loading timeout'));
        }, 10000); // 10 second timeout

        loader.load(
          url,
          (loadedTexture) => {
            clearTimeout(timeoutId);
            configureTexture(loadedTexture);
            resolve(loadedTexture);
          },
          undefined, // onProgress
          (error) => {
            clearTimeout(timeoutId);
            reject(error);
          }
        );
      });

      setTextureCache(prevCache => {
        const newCache = new Map(prevCache);
        newCache.set(id, texture);
        return newCache;
      });

      return texture;
    } catch (error) {
      // console.error(`PanoramicSphere - Error loading texture for ID ${id}:`, error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [textureCache, setTextureCache, textureLoader, configureTexture, setIsLoading]);

  // Optimized background texture loading with better performance
  useEffect(() => {
    if (loadingQueue.length === 0) return;

    let isCancelled = false;

    const loadNextTexture = async () => {
      if (isCancelled) return;

      const nextItem = loadingQueue[0];
      if (!nextItem) return;

      if (textureCache.has(nextItem._id)) {
        setLoadingQueue(prev => prev.slice(1));
        return;
      }

      try {
        setLoadingQueue(prev =>
          prev.map(item =>
            item._id === nextItem._id
              ? { ...item, status: 'downloading' }
              : item
          )
        );

        if (!isCancelled) {
          await loadTexture(nextItem.url, nextItem._id);
        }

        if (!isCancelled) {
          setLoadingQueue(prev =>
            prev.filter(item => item._id !== nextItem._id)
          );
        }
      } catch (error) {
        console.error(`Background texture loading failed for ID ${nextItem._id}:`, error);
        if (!isCancelled) {
          setLoadingQueue(prev =>
            prev.filter(item => item._id !== nextItem._id)
          );
        }
      }
    };

    const scheduleLoad = () => {
      if (typeof window !== 'undefined' && window.requestIdleCallback) {
        window.requestIdleCallback(loadNextTexture, { timeout: 1000 });
      } else {
        setTimeout(loadNextTexture, 100);
      }
    };

    scheduleLoad();

    return () => {
      isCancelled = true;
    };
  }, [loadingQueue, textureCache, setLoadingQueue, loadTexture]);

  // Optimized current texture loading with better state management
  useEffect(() => {
    if (!imageUrl || !imageId) {
      setCurrentTexture(null);
      return;
    }

    let isCancelled = false;

    const loadCurrentTexture = async () => {
      try {
        const texture = await loadTexture(imageUrl, imageId);
        if (texture && !isCancelled) {
          setCurrentTexture(texture);
          onTextureLoad?.();
        } else if (!isCancelled) {
          setCurrentTexture(null);
        }
      } catch (error) {
        console.error('Error loading current texture:', error);
        if (!isCancelled) {
          setCurrentTexture(null);
        }
      }
    };

    loadCurrentTexture();

    return () => {
      isCancelled = true;
    };
  }, [imageUrl, imageId, loadTexture, onTextureLoad]);

  // Update material when texture changes
  useEffect(() => {
    if (basicMaterial && currentTexture) {
      basicMaterial.map = currentTexture;
      basicMaterial.needsUpdate = true;
    } else if (basicMaterial) {
      basicMaterial.map = null;
      basicMaterial.needsUpdate = true;
    }
  }, [basicMaterial, currentTexture]);

  // Enhanced camera and view reset when switching images - Reset to exact currentImage values
  useEffect(() => {
    if (controlsRef.current && resetView && currentImage) {
      // Get exact values from currentImage
      const exactCameraPosition = typeof currentImage.cameraPosition === 'number' ? currentImage.cameraPosition : 0;
      const exact360Rotation = typeof currentImage._360Rotation === 'number' ? currentImage._360Rotation : 0;

      // Reset OrbitControls to default state
      controlsRef.current.reset();

      // Set camera position and target based on EXACT currentImage configuration
      if (camera) {
        // Reset camera to exact position from currentImage
        camera.position.set(0, exactCameraPosition, 0.1);
        camera.lookAt(0, exactCameraPosition, 0);

        // Update OrbitControls target to exact position
        controlsRef.current.target.set(0, exactCameraPosition, 0);
        controlsRef.current.update();
      }

      // Reset mesh rotation to EXACT currentImage rotation
      if (meshRef.current) {
        meshRef.current.rotation.set(0, exact360Rotation, 0);
      }

      // Reset Leva controls to EXACT currentImage values
      setControls({
        cameraPosition: exactCameraPosition,
        _360Rotation: exact360Rotation,
      });
    }
  }, [resetView, currentImage?._id, camera, setControls]); // Added setControls dependency

  // Leva Controls with throttling - Initialize with currentImage values:
  const [, setControls] = useControls('Controls', () => ({
    cameraPosition: {
      value: typeof currentImage?.cameraPosition === 'number' ? currentImage.cameraPosition : 0,
      min: -0.075,
      max: 0.075,
      step: 0.001,
      transient: false, // Disable transient updates for better performance
      onChange: (value) => {
        // Throttle updates to reduce message handler load
        if (debounceTimeoutRef.current) {
          clearTimeout(debounceTimeoutRef.current);
        }

        debounceTimeoutRef.current = setTimeout(() => {
          set_360Object(prev => ({ ...prev, cameraPosition: value }));
          // Manually update OrbitControls target for immediate visual feedback
          if (controlsRef.current) {
            controlsRef.current.target.y = value;
            controlsRef.current.update();
          }
        }, 50); // 50ms throttle
      }
    },
    _360Rotation: {
      value: typeof currentImage?._360Rotation === 'number' ? currentImage._360Rotation : 0,
      min: 0,
      max: Math.PI * 2,
      step: 0.01,
      transient: false, // Disable transient updates for better performance
      onChange: (value) => {
        // Throttle updates to reduce message handler load
        if (debounceTimeoutRef.current) {
          clearTimeout(debounceTimeoutRef.current);
        }

        debounceTimeoutRef.current = setTimeout(() => {
          set_360Object(prev => ({ ...prev, _360Rotation: value }));
          // Manually update mesh rotation for immediate visual feedback
          if (meshRef.current) {
            meshRef.current.rotation.y = value;
          }
        }, 50); // 50ms throttle
      }
    }
  }), [currentImage?._id]); // Depend on currentImage._id to reinitialize when image changes

  // Enhanced synchronization of Leva controls with currentImage prop
  useEffect(() => {
    if (currentImage) {
      // Ensure we have valid values with fallbacks
      const cameraPos = currentImage.cameraPosition ?? 0;
      const rotation = currentImage._360Rotation ?? 0;

      setControls({
        cameraPosition: cameraPos,
        _360Rotation: rotation,
      });

      // Directly update OrbitControls target and mesh rotation when new image loads
      if (controlsRef.current) {
        controlsRef.current.target.y = cameraPos;
        controlsRef.current.update();
      }

      if (meshRef.current) {
        meshRef.current.rotation.y = rotation;
      }

      // Update camera position to match the new image configuration
      if (camera) {
        camera.position.y = cameraPos;
        camera.updateProjectionMatrix();
      }
    }
  }, [currentImage, setControls, camera]);

  // NEW: Listen to OrbitControls' 'change' event to update Leva and parent state
  useEffect(() => {
    const controls = controlsRef.current;
    if (!controls || !meshRef.current) return;

    const handleControlsChange = () => {
      // Get the camera's current position and rotation
      // The mesh's rotation is what _360Rotation controls in this setup.
      // OrbitControls primarily controls the camera's orientation relative to the target.
      // The camera's position relative to its target defines the "cameraPosition" offset.

      // Calculate cameraPosition (vertical offset from target)
      const currentCameraY = camera.position.y;
      const targetY = controls.target.y;
      const newCameraPosition = currentCameraY - targetY; // How much camera is above/below its target

      // Get the current panorama rotation from the mesh
      const new360Rotation = meshRef.current.rotation.y;

      // Update Leva UI
      setControls({
        cameraPosition: newCameraPosition,
        _360Rotation: new360Rotation,
      });

      // Update parent state
      set_360Object(prev => ({
        ...prev,
        cameraPosition: newCameraPosition,
        _360Rotation: new360Rotation,
      }));
    };

    controls.addEventListener('change', handleControlsChange);

    return () => {
      controls.removeEventListener('change', handleControlsChange);
    };
  }, [camera, setControls, set_360Object]); // Depend on camera, setControls, and set_360Object

  // Render nothing if texture is not yet loaded (show loading state)
  if (!currentTexture) {
    return (
      <MemoizedOrbitControls
        ref={controlsRef}
        {...controlsConfig}
        target-y={currentImage?.cameraPosition || 0}
      />
    );
  }

  return (
    <>
      {/* Optimized OrbitControls for camera interaction */}
      <MemoizedOrbitControls
        ref={controlsRef}
        {...controlsConfig}
        target-y={currentImage?.cameraPosition || 0}
      />

      {/* Optimized panoramic sphere mesh */}
      <mesh
        ref={meshRef}
        rotation={[0, currentImage?._360Rotation || 0, 0]} // Initial rotation from prop
        scale={[1, 1, -1]} // Standard scale for panoramic sphere
      >
        <primitive object={sphereGeometry} />
        <primitive object={basicMaterial} />
      </mesh>
    </>
  );
}

// Export memoized component for better performance
export default memo(PanoramicSphere);